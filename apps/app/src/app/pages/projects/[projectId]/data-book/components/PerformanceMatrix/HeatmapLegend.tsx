import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';

export const HeatmapLegend = ({ leftContent }: { leftContent?: React.ReactNode }) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between px-4 min-w-max items-center">
        {leftContent}
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <div key={level} className="p-2 last:pr-0">
                <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
