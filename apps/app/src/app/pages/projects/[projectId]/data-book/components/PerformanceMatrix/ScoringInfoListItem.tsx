export type ScoringInfoListItemProps = {
  content: React.ReactNode;
  rightSlot?: string;
};
export const ScoringInfoListItem: React.FC<ScoringInfoListItemProps> = ({ content, rightSlot }) => {
  return (
    <div className="flex gap-2 justify-between items-start text-sm leading-5 font-medium">
      <span className="text-neutral">{content}</span>
      {rightSlot && <span className="text-neutral-subtlest">{rightSlot}</span>}
    </div>
  );
};
