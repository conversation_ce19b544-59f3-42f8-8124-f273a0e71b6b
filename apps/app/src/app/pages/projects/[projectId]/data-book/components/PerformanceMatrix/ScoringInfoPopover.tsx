import Button from "@shape-construction/arch-ui/src/Button";
import Popover from "@shape-construction/arch-ui/src/Popover";
import { Tooltip, TooltipContent, TooltipTrigger } from "@shape-construction/arch-ui/src/Tooltip/Tooltip";
import { ScoringInfo } from "./ScoringInfo";
import { InformationCircleIcon } from "@shape-construction/arch-ui/src/Icons/solid";
import { useMessageGetter } from "@messageformat/react";

export const ScoringInfoPopover: React.FC = () => {
    const messages = useMessageGetter('dataBook.page.heatmapDashboard');
    return <Popover>
        <Popover.Trigger>
            <Tooltip delayDuration={100}>
                <TooltipTrigger>
                    <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                        {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
                    </Button>
                </TooltipTrigger>
                <TooltipContent align="start">
                    {messages('performanceDetails.issueReportsTable.scoringInfo.tooltipText')}
                </TooltipContent>
            </Tooltip>
        </Popover.Trigger>
        <Popover.Content align="start" className="p-0 max-h-[320px]">
            <ScoringInfo />
        </Popover.Content>
    </Popover>
};
