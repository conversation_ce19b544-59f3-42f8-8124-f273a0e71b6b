import React from 'react';
import type { DataHealthDashboardScoreSchema } from '@shape-construction/api/src/types';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { PerformanceDetailsModal } from '../components/PerformanceDetailsModal/PerformanceDetailsModal';
import { PerformanceMatrix } from '../components/PerformanceMatrix/PerformanceMatrix';
import { usePerformanceDetailsModal } from '../hooks/usePerformanceDetailsModal';
import { useDataHealth } from './DataHealthContext';
import { ScoringInfoPopover } from '../components/PerformanceMatrix/ScoringInfoPopover';

export const DataHealthDashboard = () => {
  const { data, recordType, isLoading, metadata } = useDataHealth();

  const { modalOptions, closeModal, openModal } = usePerformanceDetailsModal();

  const handleOnCellClick = (dataPoint: DataHealthDashboardScoreSchema) => {
    openModal({ recordType, ...dataPoint });
  };

  if (isLoading || !metadata) return <LoadingSpinner variant="screen" />;

  return (
    <>
      <PerformanceMatrix data={data} onCellClick={handleOnCellClick} leftContent={recordType === 'issues' && <ScoringInfoPopover />}
      />
      {modalOptions.open && <PerformanceDetailsModal onClose={closeModal} {...modalOptions} />}
    </>
  );
};
